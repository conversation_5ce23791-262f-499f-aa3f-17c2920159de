#!/usr/bin/env python3
"""
测试标题替换功能的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.app.service.bug_evaluation_service import get_title_suggestion_by_bug_id
from backend.app.utils.tapd import tap_client
from backend.app.config.config import BUG_EVALUATION_WORKSPACE_ID
from backend.app.utils.logger_util import logger


async def test_get_title_suggestion():
    """测试获取标题建议功能"""
    print("=== 测试获取标题建议功能 ===")
    
    try:
        # 首先测试数据库连接和查询逻辑
        from backend.app.database.database import get_db
        from backend.app.models.bug import BugEvaluation, TitleEvaluation
        
        db_gen = get_db()
        db = next(db_gen)
        
        # 查找一个有标题评估记录的bug_id
        result = db.query(BugEvaluation.bug_id, TitleEvaluation.suggest).join(
            TitleEvaluation, TitleEvaluation.bug_evaluation_id == BugEvaluation.id
        ).filter(
            TitleEvaluation.suggest.isnot(None),
            TitleEvaluation.suggest != ""
        ).first()
        
        db.close()
        
        if not result:
            print("⚠️  数据库中没有找到有标题建议的记录，跳过此测试")
            return True  # 不算失败，因为可能是测试环境没有数据
        
        test_bug_id = result[0]
        expected_suggestion = result[1]
        
        print(f"使用测试BUG ID: {test_bug_id}")
        
        # 测试我们的函数
        suggestion = await get_title_suggestion_by_bug_id(test_bug_id)
        if suggestion:
            print(f"✅ 成功获取标题建议: {suggestion}")
            if suggestion == expected_suggestion:
                print("✅ 建议内容与数据库记录一致")
            else:
                print(f"⚠️  建议内容与预期不一致，预期: {expected_suggestion}")
            return True
        else:
            print(f"❌ 未找到BUG {test_bug_id} 的标题建议")
            return False
            
    except Exception as e:
        print(f"❌ 获取标题建议时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_tapd_client():
    """测试TAPD客户端的标题更新功能"""
    print("\n=== 测试TAPD客户端标题更新功能 ===")
    
    # 检查TAPD客户端是否有update_bug_title方法
    if hasattr(tap_client, 'update_bug_title'):
        print("✅ TAPD客户端已添加update_bug_title方法")
        
        # 检查方法签名
        import inspect
        sig = inspect.signature(tap_client.update_bug_title)
        params = list(sig.parameters.keys())
        expected_params = ['workspace_id', 'bug_id', 'title']
        
        if all(param in params for param in expected_params):
            print("✅ update_bug_title方法参数正确")
            return True
        else:
            print(f"❌ update_bug_title方法参数不正确，期望: {expected_params}, 实际: {params}")
            return False
    else:
        print("❌ TAPD客户端缺少update_bug_title方法")
        return False


def test_button_configuration():
    """测试按钮配置"""
    print("\n=== 测试按钮配置 ===")
    
    try:
        from backend.app.service.cards import build_feedback_buttons_individual
        
        # 测试按钮生成（title_pass=False时应该包含标题相关按钮）
        buttons = build_feedback_buttons_individual("test_bug_id", True, False)
        
        # 检查是否包含标题替换按钮
        title_replace_found = False
        for button in buttons:
            for action in button.get("actions", []):
                if action.get("value") == "title_replace":
                    title_replace_found = True
                    print(f"✅ 找到标题替换按钮: {action.get('text')}")
                    print(f"   按钮颜色: {action.get('border_color')}")
                    print(f"   替换文本: {action.get('replace_text')}")
                    break
        
        if not title_replace_found:
            print("❌ 未找到标题替换按钮")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 测试按钮配置时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_process_chat_feedback():
    """测试聊天反馈处理逻辑"""
    print("\n=== 测试聊天反馈处理逻辑 ===")
    
    try:
        from backend.app.handlers.handlers import process_chat_feedback
        
        # 检查函数是否存在
        print("✅ process_chat_feedback函数存在")
        
        # 检查函数签名
        import inspect
        sig = inspect.signature(process_chat_feedback)
        params = list(sig.parameters.keys())
        expected_params = ['bug_id', 'action_value', 'operator']
        
        if all(param in params for param in expected_params):
            print("✅ process_chat_feedback函数参数正确")
            return True
        else:
            print(f"❌ process_chat_feedback函数参数不正确，期望: {expected_params}, 实际: {params}")
            return False
            
    except Exception as e:
        print(f"❌ 测试聊天反馈处理逻辑时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("开始测试标题替换功能...\n")
    
    tests = [
        ("TAPD客户端功能", test_tapd_client),
        ("按钮配置", test_button_configuration),
        ("聊天反馈处理逻辑", test_process_chat_feedback),
        ("获取标题建议", test_get_title_suggestion),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"运行测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 时发生异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！标题替换功能已成功实现。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")


if __name__ == "__main__":
    asyncio.run(main())
